#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON>retta<PERSON>@inria.fr
#

import os
import json
import torch
import numpy as np
from PIL import Image
import torchvision.transforms.functional as tf
import torchvision
import matplotlib.pyplot as plt
import matplotlib
from pathlib import Path
from argparse import ArgumentParser
from tqdm import tqdm
import shutil

matplotlib.rc("font",family='Microsoft YaHei')
def load_json(json_path):
    """加载JSON文件"""
    with open(json_path, 'r') as f:
        return json.load(f)

def get_all_images_with_metrics(baseline_data, current_data, metric):
    """获取所有图像的指标数据"""
    diff_dict = {}
    
    # 确保我们比较的是相同的图像集
    baseline_images = set(baseline_data["test"]["ours_30000"][metric].keys())
    current_images = set(current_data["ours_50000"][metric].keys())
    common_images = baseline_images.intersection(current_images)
    
    if not common_images:
        raise ValueError(f"没有找到共同的图像来比较 {metric} 指标")
    
    # 计算每张图像的指标差异
    for img_name in common_images:
        baseline_value = baseline_data["test"]["ours_30000"][metric][img_name]
        current_value = current_data["ours_50000"][metric][img_name]
        
        # 对于LPIPS，较小的值更好；对于SSIM和PSNR，较大的值更好
        if metric == "LPIPS":
            diff = baseline_value - current_value  # 正值表示当前方法更好
        else:
            diff = current_value - baseline_value  # 正值表示当前方法更好
        
        diff_dict[img_name] = {
            'baseline_value': baseline_value,
            'current_value': current_value,
            'diff': diff
        }
    
    # 按差异排序并返回所有图像（差异从大到小）
    sorted_diffs = sorted(diff_dict.items(), key=lambda x: x[1]['diff'], reverse=True)
    return sorted_diffs

def load_image(image_path, target_size=None):
    """加载图像并转换为PyTorch张量"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"找不到图像: {image_path}")
    
    # 尝试加载PNG图像
    if image_path.endswith('.png'):
        img = Image.open(image_path).convert("RGB")
        tensor = tf.to_tensor(img)
    # 尝试加载NPY文件
    elif image_path.endswith('.npy'):
        img_np = np.load(image_path)
        # 如果是HWC格式，转换为CHW
        if img_np.shape[-1] == 3:
            img_np = np.transpose(img_np, (2, 0, 1))
        tensor = torch.from_numpy(img_np).float()
    else:
        raise ValueError(f"不支持的图像格式: {image_path}")
    
    # 如果指定了目标尺寸，进行resize
    if target_size is not None:
        tensor = tf.resize(tensor, target_size)
    
    return tensor

def create_diff_image(img1, img2):
    """创建两个图像之间的差异图"""
    # 计算绝对差异
    diff = torch.abs(img1 - img2).mean(dim=0)
    
    # 归一化差异以便更好地可视化
    diff = diff - diff.min() / (diff.max() - diff.min() + 1e-8)
    
    # 使用热力图颜色映射
    # 红色表示差异大，蓝色表示差异小
    r = torch.clamp(diff * 2, 0, 1)
    g = torch.clamp(2 - diff * 4, 0, 1) * torch.clamp(diff * 4, 0, 1)
    b = torch.clamp(1 - diff * 2, 0, 1)
    
    return torch.stack([r, g, b], dim=0)

def process_single_image(baseline_dir, current_dir, img_name, img_data, metric, output_dir, rank):
    """处理单张图像的比较"""
    img_base_name = os.path.splitext(img_name)[0]
    
    # 创建图像子目录
    img_dir = os.path.join(output_dir, f"{rank:03d}_{img_base_name}")
    os.makedirs(img_dir, exist_ok=True)
    
    try:
        # 基准渲染图
        baseline_render_path = os.path.join(baseline_dir, "test", "ours_30000", "renders", img_name)
        baseline_render = load_image(baseline_render_path)
        target_size = baseline_render.shape[1:] 
        
        # 当前渲染图
        current_render_path = os.path.join(current_dir, "test", "ours_50000", "renders", img_name)
        current_render = load_image(current_render_path, target_size)
        
        # 真值图
        gt_path = os.path.join(current_dir, "test", "ours_50000", "gt", img_name)
        gt_image = load_image(gt_path, target_size)
        
        # 创建差异图
        diff_image = create_diff_image(baseline_render, current_render)
        
        # 保存图像
        torchvision.utils.save_image(baseline_render, os.path.join(img_dir, "baseline.png"))
        torchvision.utils.save_image(current_render, os.path.join(img_dir, "current.png"))
        torchvision.utils.save_image(gt_image, os.path.join(img_dir, "gt.png"))
        torchvision.utils.save_image(diff_image, os.path.join(img_dir, "diff.png"))
        
        # 创建组合图像以便比较
        combined = torch.cat([
            torch.cat([baseline_render, current_render], dim=2),
            torch.cat([gt_image, diff_image], dim=2)
        ], dim=1)
        
        torchvision.utils.save_image(combined, os.path.join(img_dir, "combined.png"))
        
        # 创建信息文本文件
        with open(os.path.join(img_dir, "info.txt"), "w") as f:
            f.write(f"排名: {rank}\n")
            f.write(f"图像: {img_name}\n")
            f.write(f"基准 {metric}: {img_data['baseline_value']:.6f}\n")
            f.write(f"当前 {metric}: {img_data['current_value']:.6f}\n")
            f.write(f"差异: {img_data['diff']:.6f}\n")
            if metric == "LPIPS":
                f.write("注意: LPIPS值越小越好\n")
            else:
                f.write(f"注意: {metric}值越大越好\n")
        
        return {
            "rank": rank,
            "image_name": img_name,
            "baseline_value": img_data['baseline_value'],
            "current_value": img_data['current_value'],
            "diff_value": img_data['diff'],
            "output_dir": img_dir
        }
        
    except Exception as e:
        print(f"处理图像 {img_name} 时出错: {str(e)}")
        return None

def process_comparison_all(baseline_dir, current_dir, output_dir):
    """处理两个文件夹之间的所有图像比较并生成可视化结果"""
    # 加载per_view.json文件
    baseline_json_path = os.path.join(baseline_dir, "per_view.json")
    current_json_path = os.path.join(current_dir, "per_view.json")

    if not os.path.exists(baseline_json_path):
        raise FileNotFoundError(f"找不到基准文件: {baseline_json_path}")
    if not os.path.exists(current_json_path):
        raise FileNotFoundError(f"找不到当前文件: {current_json_path}")

    baseline_data = load_json(baseline_json_path)
    current_data = load_json(current_json_path)

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 对每个指标处理所有图像
    metrics = ["SSIM", "PSNR", "LPIPS"]
    all_results = {}

    for metric in metrics:
        print(f"处理 {metric} 指标的所有图像...")
        all_images_data = get_all_images_with_metrics(baseline_data, current_data, metric)

        # 创建指标子目录
        metric_dir = os.path.join(output_dir, metric)
        os.makedirs(metric_dir, exist_ok=True)

        all_results[metric] = []

        # 使用tqdm显示进度条
        for rank, (img_name, img_data) in enumerate(tqdm(all_images_data, desc=f"处理 {metric} 指标"), 1):
            result = process_single_image(
                baseline_dir, current_dir, img_name, img_data,
                metric, metric_dir, rank
            )

            if result is not None:
                all_results[metric].append(result)

                # 每处理10张图像输出一次进度信息
                if rank % 10 == 0:
                    print(f"  已处理 {rank}/{len(all_images_data)} 张图像")

    # 创建总结报告
    create_summary_report(baseline_dir, current_dir, output_dir, all_results, metrics)

    # 创建统计分析
    create_statistics_analysis(output_dir, all_results, metrics)

    print(f"\n所有图像比较完成。结果保存在 {output_dir}")
    return all_results

def create_summary_report(baseline_dir, current_dir, output_dir, all_results, metrics):
    """创建详细的总结报告"""
    with open(os.path.join(output_dir, "summary_all.txt"), "w") as f:
        f.write(f"基准目录: {baseline_dir}\n")
        f.write(f"当前目录: {current_dir}\n\n")

        for metric in metrics:
            results = all_results[metric]
            total_images = len(results)

            f.write(f"\n{'='*50}\n")
            f.write(f"{metric} 指标 - 所有 {total_images} 张图像的比较结果:\n")
            f.write(f"{'='*50}\n")

            # 计算统计信息
            diffs = [r['diff_value'] for r in results]
            baseline_values = [r['baseline_value'] for r in results]
            current_values = [r['current_value'] for r in results]

            f.write(f"\n统计信息:\n")
            f.write(f"  平均差异: {np.mean(diffs):.6f}\n")
            f.write(f"  差异标准差: {np.std(diffs):.6f}\n")
            f.write(f"  最大差异: {np.max(diffs):.6f}\n")
            f.write(f"  最小差异: {np.min(diffs):.6f}\n")
            f.write(f"  基准平均值: {np.mean(baseline_values):.6f}\n")
            f.write(f"  当前平均值: {np.mean(current_values):.6f}\n")

            # 改进图像数量统计
            improved_count = sum(1 for d in diffs if d > 0)
            degraded_count = sum(1 for d in diffs if d < 0)
            unchanged_count = sum(1 for d in diffs if d == 0)

            f.write(f"\n性能变化统计:\n")
            f.write(f"  改进的图像: {improved_count} ({improved_count/total_images*100:.1f}%)\n")
            f.write(f"  退化的图像: {degraded_count} ({degraded_count/total_images*100:.1f}%)\n")
            f.write(f"  无变化的图像: {unchanged_count} ({unchanged_count/total_images*100:.1f}%)\n")

            f.write(f"\n详细结果 (按差异排序):\n")
            f.write(f"{'排名':<6} {'图像名称':<15} {'基准值':<12} {'当前值':<12} {'差异':<12}\n")
            f.write(f"{'-'*65}\n")

            for result in results:
                f.write(f"{result['rank']:<6} {result['image_name']:<15} "
                       f"{result['baseline_value']:<12.6f} {result['current_value']:<12.6f} "
                       f"{result['diff_value']:<12.6f}\n")

def create_statistics_analysis(output_dir, all_results, metrics):
    """创建统计分析图表"""
    try:
        import matplotlib.pyplot as plt

        # 创建统计图表目录
        stats_dir = os.path.join(output_dir, "statistics")
        os.makedirs(stats_dir, exist_ok=True)

        for metric in metrics:
            results = all_results[metric]
            diffs = [r['diff_value'] for r in results]
            baseline_values = [r['baseline_value'] for r in results]
            current_values = [r['current_value'] for r in results]

            # 创建差异分布直方图
            plt.figure(figsize=(12, 8))

            plt.subplot(2, 2, 1)
            plt.hist(diffs, bins=30, alpha=0.7, color='blue', edgecolor='black')
            plt.title(f'{metric} 差异分布')
            plt.xlabel('差异值')
            plt.ylabel('图像数量')
            plt.axvline(x=0, color='red', linestyle='--', label='无差异线')
            plt.legend()

            # 创建基准值vs当前值散点图
            plt.subplot(2, 2, 2)
            plt.scatter(baseline_values, current_values, alpha=0.6)
            plt.plot([min(baseline_values), max(baseline_values)],
                    [min(baseline_values), max(baseline_values)],
                    'r--', label='y=x (无变化线)')
            plt.xlabel('基准值')
            plt.ylabel('当前值')
            plt.title(f'{metric} 基准值 vs 当前值')
            plt.legend()

            # 创建差异排序图
            plt.subplot(2, 2, 3)
            sorted_diffs = sorted(diffs, reverse=True)
            plt.plot(range(len(sorted_diffs)), sorted_diffs, 'b-', linewidth=2)
            plt.xlabel('图像排名')
            plt.ylabel('差异值')
            plt.title(f'{metric} 差异排序')
            plt.axhline(y=0, color='red', linestyle='--', alpha=0.7)

            # 创建累积分布图
            plt.subplot(2, 2, 4)
            sorted_diffs_for_cdf = sorted(diffs)
            y_vals = np.arange(1, len(sorted_diffs_for_cdf) + 1) / len(sorted_diffs_for_cdf)
            plt.plot(sorted_diffs_for_cdf, y_vals, 'g-', linewidth=2)
            plt.xlabel('差异值')
            plt.ylabel('累积概率')
            plt.title(f'{metric} 差异累积分布')
            plt.axvline(x=0, color='red', linestyle='--', alpha=0.7)

            plt.tight_layout()
            plt.savefig(os.path.join(stats_dir, f'{metric}_analysis.png'), dpi=300, bbox_inches='tight')
            plt.close()

        print(f"统计分析图表已保存到 {stats_dir}")

    except ImportError:
        print("matplotlib未安装，跳过统计图表生成")
    except Exception as e:
        print(f"生成统计图表时出错: {str(e)}")

if __name__ == "__main__":
    parser = ArgumentParser(description="比较两个文件夹中的所有渲染结果并生成可视化")
    parser.add_argument("--baseline_dir", default="../gaussian-splatting/output/bicycle_1200_eval", help="基准文件夹路径")
    parser.add_argument("--current_dir", default="output/bicycle/imp_score_opa_origin", help="当前文件夹路径")
    parser.add_argument("--output_dir", default="output/bicycle/imp_score_opa_origin/comparison_all", help="输出文件夹路径")

    args = parser.parse_args()

    process_comparison_all(args.baseline_dir, args.current_dir, args.output_dir)
