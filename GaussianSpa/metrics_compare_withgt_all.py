#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

import os
import json
import torch
import numpy as np
from PIL import Image
import torchvision.transforms.functional as tf
import torchvision
import matplotlib.pyplot as plt
from pathlib import Path
from argparse import ArgumentParser
from tqdm import tqdm
import shutil

def load_json(json_path):
    """加载JSON文件"""
    with open(json_path, 'r') as f:
        return json.load(f)

def get_all_images_with_metrics(baseline_data, current_data, metric):
    """获取所有图像的指标数据"""
    diff_dict = {}
    
    # 确保我们比较的是相同的图像集
    # baseline_images = set(baseline_data["test"]["ours_30000"][metric].keys())
    baseline_images = set(baseline_data["ours_50000"][metric].keys())
    current_images = set(current_data["ours_50000"][metric].keys())
    common_images = baseline_images.intersection(current_images)

    if not common_images:
        raise ValueError(f"没有找到共同的图像来比较 {metric} 指标")

    # 计算每张图像的指标差异
    for img_name in common_images:
        # baseline_value = baseline_data["test"]["ours_30000"][metric][img_name]
        baseline_value = baseline_data["ours_50000"][metric][img_name]
        current_value = current_data["ours_50000"][metric][img_name]
        
        # 对于LPIPS，较小的值更好；对于SSIM和PSNR，较大的值更好
        if metric == "LPIPS":
            diff = baseline_value - current_value  # 正值表示当前方法更好
        else:
            diff = current_value - baseline_value  # 正值表示当前方法更好
        
        diff_dict[img_name] = {
            'baseline_value': baseline_value,
            'current_value': current_value,
            'diff': diff
        }
    
    # 按差异排序并返回所有图像（差异从大到小）
    sorted_diffs = sorted(diff_dict.items(), key=lambda x: x[1]['diff'], reverse=True)
    return sorted_diffs

def load_image(image_path, target_size=None):
    """加载图像并转换为PyTorch张量"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"找不到图像: {image_path}")
    
    # 尝试加载PNG图像
    if image_path.endswith('.png'):
        img = Image.open(image_path).convert("RGB")
        tensor = tf.to_tensor(img)
    # 尝试加载NPY文件
    elif image_path.endswith('.npy'):
        img_np = np.load(image_path)
        # 如果是HWC格式，转换为CHW
        if img_np.shape[-1] == 3:
            img_np = np.transpose(img_np, (2, 0, 1))
        tensor = torch.from_numpy(img_np).float()
    else:
        raise ValueError(f"不支持的图像格式: {image_path}")
    
    # 如果指定了目标尺寸，进行resize
    if target_size is not None:
        tensor = tf.resize(tensor, target_size)
    
    return tensor

def create_gt_comparison_diff_image(baseline_render, current_render, gt_image):
    """创建基于真值图比较的差异图
    
    Args:
        baseline_render: 基准渲染图 (C, H, W)
        current_render: 当前渲染图 (C, H, W)
        gt_image: 真值图 (C, H, W)
    
    Returns:
        diff_image: 差异图 (3, H, W)
            红色区域: 基准渲染图与真值图更接近
            蓝色区域: 当前渲染图与真值图更接近
            绿色区域: 两者与真值图差异相近
    """
    # 计算基准渲染图与真值图的差异 (逐像素L2距离)
    baseline_diff = torch.sqrt(torch.sum((baseline_render - gt_image) ** 2, dim=0))
    
    # 计算当前渲染图与真值图的差异 (逐像素L2距离)
    current_diff = torch.sqrt(torch.sum((current_render - gt_image) ** 2, dim=0))
    
    # 计算差异的差值：正值表示当前渲染图更好，负值表示基准渲染图更好
    diff_comparison = baseline_diff - current_diff
    
    # 归一化到[-1, 1]范围
    max_abs_diff = torch.max(torch.abs(diff_comparison))
    if max_abs_diff > 0:
        diff_comparison = diff_comparison / max_abs_diff
    
    # 创建颜色编码的差异图
    # 红色通道：基准渲染图更好的区域 (diff_comparison < 0)
    # 蓝色通道：当前渲染图更好的区域 (diff_comparison > 0)
    # 绿色通道：差异较小的区域
    
    red_channel = torch.clamp(-diff_comparison, 0, 1)  # 基准更好时为红色
    blue_channel = torch.clamp(diff_comparison, 0, 1)  # 当前更好时为蓝色
    
    # 绿色通道表示差异较小的区域
    green_channel = 1.0 - torch.abs(diff_comparison)
    green_channel = torch.clamp(green_channel * 0.5, 0, 0.5)  # 降低绿色强度
    
    # 组合RGB通道
    diff_image = torch.stack([red_channel, green_channel, blue_channel], dim=0)
    
    return diff_image

def create_individual_diff_images(baseline_render, current_render, gt_image):
    """创建单独的差异图用于可视化"""
    # 当前渲染图与真值图的差异
    current_gt_diff = torch.abs(current_render - gt_image).mean(dim=0)
    current_gt_diff = (current_gt_diff - current_gt_diff.min()) / (current_gt_diff.max() - current_gt_diff.min() + 1e-8)

    # 计算基准渲染图与真值图的差异 (逐像素L2距离)
    baseline_diff = torch.sqrt(torch.sum((baseline_render - gt_image) ** 2, dim=0))

    # 计算当前渲染图与真值图的差异 (逐像素L2距离)
    current_diff = torch.sqrt(torch.sum((current_render - gt_image) ** 2, dim=0))

    # 计算差异的差值：正值表示当前渲染图更好，负值表示基准渲染图更好
    diff_comparison = baseline_diff - current_diff

    # 创建baseline更好的区域图 (baseline效果优于current的像素越好越红)
    baseline_better_mask = diff_comparison < 0  # baseline更好的区域
    baseline_better_intensity = torch.abs(diff_comparison) * baseline_better_mask.float()
    if baseline_better_intensity.max() > 0:
        baseline_better_intensity = baseline_better_intensity / baseline_better_intensity.max()

    baseline_better_red = baseline_better_intensity
    baseline_better_blue = (~baseline_better_mask).float()
    baseline_better_green = torch.zeros_like(baseline_better_red)
    baseline_better_image = torch.stack([baseline_better_red, baseline_better_green, baseline_better_blue], dim=0)

    # 创建current更好的区域图 (current效果优于baseline的像素越好越红)
    current_better_mask = diff_comparison > 0  # current更好的区域
    current_better_intensity = torch.abs(diff_comparison) * current_better_mask.float()
    if current_better_intensity.max() > 0:
        current_better_intensity = current_better_intensity / current_better_intensity.max()

    current_better_red = current_better_intensity
    current_better_blue = (~current_better_mask).float()
    current_better_green = torch.zeros_like(current_better_red)
    current_better_image = torch.stack([current_better_red, current_better_green, current_better_blue], dim=0)

    # 转换current_gt_diff为热力图颜色
    def to_heatmap(diff_map):
        r = torch.clamp(diff_map * 2, 0, 1)
        g = torch.clamp(2 - diff_map * 4, 0, 1) * torch.clamp(diff_map * 4, 0, 1)
        b = torch.clamp(1 - diff_map * 2, 0, 1)
        return torch.stack([r, g, b], dim=0)

    current_gt_heatmap = to_heatmap(current_gt_diff)

    return baseline_better_image, current_better_image, current_gt_heatmap

def add_text_label(image_tensor, text, position='top-left', font_size=20, color=(1.0, 1.0, 1.0)):
    """在图像张量上添加文字标签

    Args:
        image_tensor: 图像张量 (C, H, W)
        text: 要添加的文字
        position: 文字位置 ('top-left', 'top-right', 'bottom-left', 'bottom-right')
        font_size: 字体大小
        color: 文字颜色 (R, G, B)

    Returns:
        带标签的图像张量
    """
    # 将张量转换为PIL图像
    pil_image = tf.to_pil_image(image_tensor)

    # 创建绘图对象
    from PIL import ImageDraw, ImageFont
    draw = ImageDraw.Draw(pil_image)

    # 尝试加载字体，如果失败则使用默认字体
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
    except:
        try:
            # 备选字体路径
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # 使用默认字体
            font = ImageFont.load_default()

    # 获取文字尺寸
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]

    # 计算文字位置
    img_width, img_height = pil_image.size
    margin = 10

    if position == 'top-left':
        x, y = margin, margin
    elif position == 'top-right':
        x, y = img_width - text_width - margin, margin
    elif position == 'bottom-left':
        x, y = margin, img_height - text_height - margin
    elif position == 'bottom-right':
        x, y = img_width - text_width - margin, img_height - text_height - margin
    else:
        x, y = margin, margin

    # 绘制文字背景（半透明黑色矩形）
    bg_color = (0, 0, 0, 128)  # 半透明黑色
    draw.rectangle([x-5, y-2, x+text_width+5, y+text_height+2], fill=bg_color)

    # 绘制文字
    text_color = tuple(int(c * 255) for c in color)
    draw.text((x, y), text, font=font, fill=text_color)

    # 转换回张量
    return tf.to_tensor(pil_image)

def create_labeled_combined_image(baseline_render, current_render, gt_image,
                                baseline_better_image, current_better_image, current_gt_diff):
    """创建带标签的组合图像"""

    # 为每个图像添加标签
    labeled_baseline = add_text_label(baseline_render, "Baseline", "top-left")
    labeled_current = add_text_label(current_render, "Current", "top-left")
    labeled_gt = add_text_label(gt_image, "Ground Truth", "top-left")
    labeled_baseline_better = add_text_label(baseline_better_image, "Baseline Better\n(Red=Better)", "top-left")
    labeled_current_better = add_text_label(current_better_image, "Current Better\n(Red=Better)", "top-left")
    labeled_current_gt = add_text_label(current_gt_diff, "Current vs GT", "top-left")

    # 创建组合图像
    # 第一行：基准渲染图、当前渲染图、真值图
    # 第二行：baseline更好区域、current更好区域、current vs GT
    combined = torch.cat([
        torch.cat([labeled_baseline, labeled_current, labeled_gt], dim=2),
        torch.cat([labeled_baseline_better, labeled_current_better, labeled_current_gt], dim=2)
    ], dim=1)

    return combined

def process_single_image(baseline_dir, current_dir, img_name, img_data, metric, output_dir, rank):
    """处理单张图像的比较"""
    img_base_name = os.path.splitext(img_name)[0]

    # 创建图像子目录
    img_dir = os.path.join(output_dir, f"{rank:03d}_{img_base_name}")
    os.makedirs(img_dir, exist_ok=True)

    try:
        # 基准渲染图
        # baseline_render_path = os.path.join(baseline_dir, "test", "ours_30000", "renders", img_name)
        baseline_render_path = os.path.join(baseline_dir, "test", "ours_50000", "renders", img_name)
        baseline_render = load_image(baseline_render_path)
        target_size = baseline_render.shape[1:]

        # 当前渲染图
        current_render_path = os.path.join(current_dir, "test", "ours_50000", "renders", img_name)
        current_render = load_image(current_render_path, target_size)

        # 真值图
        gt_path = os.path.join(current_dir, "test", "ours_50000", "gt", img_name)
        gt_image = load_image(gt_path, target_size)

        # 创建基于真值图比较的差异图
        gt_comparison_diff = create_gt_comparison_diff_image(baseline_render, current_render, gt_image)

        # 创建单独的差异图
        baseline_better_image, current_better_image, current_gt_diff = create_individual_diff_images(baseline_render, current_render, gt_image)

        # 保存图像
        torchvision.utils.save_image(baseline_render, os.path.join(img_dir, "baseline.png"))
        torchvision.utils.save_image(current_render, os.path.join(img_dir, "current.png"))
        torchvision.utils.save_image(gt_image, os.path.join(img_dir, "gt.png"))
        torchvision.utils.save_image(gt_comparison_diff, os.path.join(img_dir, "gt_comparison_diff.png"))
        torchvision.utils.save_image(baseline_better_image, os.path.join(img_dir, "baseline_better.png"))
        torchvision.utils.save_image(current_better_image, os.path.join(img_dir, "current_better.png"))
        torchvision.utils.save_image(current_gt_diff, os.path.join(img_dir, "current_gt_diff.png"))

        # 创建带标签的组合图像
        combined = create_labeled_combined_image(
            baseline_render, current_render, gt_image,
            baseline_better_image, current_better_image, current_gt_diff
        )

        torchvision.utils.save_image(combined, os.path.join(img_dir, "combined.png"))

        # 创建信息文本文件
        with open(os.path.join(img_dir, "info.txt"), "w") as f:
            f.write(f"Rank: {rank}\n")
            f.write(f"Image: {img_name}\n")
            f.write(f"Baseline {metric}: {img_data['baseline_value']:.6f}\n")
            f.write(f"Current {metric}: {img_data['current_value']:.6f}\n")
            f.write(f"Difference: {img_data['diff']:.6f}\n")
            f.write(f"\nGenerated files:\n")
            f.write(f"- baseline.png: Baseline rendering\n")
            f.write(f"- current.png: Current rendering\n")
            f.write(f"- gt.png: Ground truth image\n")
            f.write(f"- baseline_better.png: Regions where baseline is better (red=better, blue=worse)\n")
            f.write(f"- current_better.png: Regions where current is better (red=better, blue=worse)\n")
            f.write(f"- current_gt_diff.png: Difference between current and ground truth (heatmap)\n")
            f.write(f"- gt_comparison_diff.png: Comparison difference (red-blue encoding)\n")
            f.write(f"- combined.png: All images in a 2x3 grid with labels\n")
            f.write(f"\nCombined image layout:\n")
            f.write(f"Top row:    [Baseline] [Current] [Ground Truth]\n")
            f.write(f"Bottom row: [Baseline Better] [Current Better] [Current vs GT]\n")
            f.write(f"\nColor coding in comparison images:\n")
            f.write(f"- Red regions: Baseline render is closer to ground truth\n")
            f.write(f"- Blue regions: Current render is closer to ground truth\n")
            f.write(f"- Green regions: Similar distance to ground truth\n")
            if metric == "LPIPS":
                f.write("Note: Lower LPIPS values are better\n")
            else:
                f.write(f"Note: Higher {metric} values are better\n")

        return {
            "rank": rank,
            "image_name": img_name,
            "baseline_value": img_data['baseline_value'],
            "current_value": img_data['current_value'],
            "diff_value": img_data['diff'],
            "output_dir": img_dir
        }

    except Exception as e:
        print(f"Error processing image {img_name}: {str(e)}")
        return None

def process_comparison_all(baseline_dir, current_dir, output_dir):
    """处理两个文件夹之间的所有图像比较并生成可视化结果"""
    # 加载per_view.json文件
    baseline_json_path = os.path.join(baseline_dir, "per_view.json")
    current_json_path = os.path.join(current_dir, "per_view.json")

    if not os.path.exists(baseline_json_path):
        raise FileNotFoundError(f"Baseline file not found: {baseline_json_path}")
    if not os.path.exists(current_json_path):
        raise FileNotFoundError(f"Current file not found: {current_json_path}")

    baseline_data = load_json(baseline_json_path)
    current_data = load_json(current_json_path)

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 对每个指标处理所有图像
    metrics = ["SSIM", "PSNR", "LPIPS"]
    all_results = {}

    for metric in metrics:
        print(f"Processing all images for {metric} metric...")
        all_images_data = get_all_images_with_metrics(baseline_data, current_data, metric)

        # 创建指标子目录
        metric_dir = os.path.join(output_dir, metric)
        os.makedirs(metric_dir, exist_ok=True)

        all_results[metric] = []

        # 使用tqdm显示进度条
        for rank, (img_name, img_data) in enumerate(tqdm(all_images_data, desc=f"Processing {metric} metric"), 1):
            result = process_single_image(
                baseline_dir, current_dir, img_name, img_data,
                metric, metric_dir, rank
            )

            if result is not None:
                all_results[metric].append(result)

                # 每处理10张图像输出一次进度信息
                if rank % 10 == 0:
                    print(f"  Processed {rank}/{len(all_images_data)} images")

    # 创建总结报告
    create_summary_report(baseline_dir, current_dir, output_dir, all_results, metrics)

    # 创建统计分析
    create_statistics_analysis(output_dir, all_results, metrics)

    print(f"\nAll image comparisons completed. Results saved to {output_dir}")
    return all_results

def create_summary_report(baseline_dir, current_dir, output_dir, all_results, metrics):
    """创建详细的总结报告"""
    with open(os.path.join(output_dir, "summary_all_withgt.txt"), "w") as f:
        f.write(f"Baseline directory: {baseline_dir}\n")
        f.write(f"Current directory: {current_dir}\n\n")
        f.write(f"Comparison method: Both baseline and current renders compared against ground truth\n")
        f.write(f"Color coding in difference images:\n")
        f.write(f"  - Red regions: Baseline render closer to ground truth\n")
        f.write(f"  - Blue regions: Current render closer to ground truth\n")
        f.write(f"  - Green regions: Similar distance to ground truth\n\n")

        for metric in metrics:
            results = all_results[metric]
            total_images = len(results)

            f.write(f"\n{'='*50}\n")
            f.write(f"{metric} Metric - Comparison results for all {total_images} images:\n")
            f.write(f"{'='*50}\n")

            # 计算统计信息
            diffs = [r['diff_value'] for r in results]
            baseline_values = [r['baseline_value'] for r in results]
            current_values = [r['current_value'] for r in results]

            f.write(f"\nStatistics:\n")
            f.write(f"  Average difference: {np.mean(diffs):.6f}\n")
            f.write(f"  Difference std dev: {np.std(diffs):.6f}\n")
            f.write(f"  Maximum difference: {np.max(diffs):.6f}\n")
            f.write(f"  Minimum difference: {np.min(diffs):.6f}\n")
            f.write(f"  Baseline average: {np.mean(baseline_values):.6f}\n")
            f.write(f"  Current average: {np.mean(current_values):.6f}\n")

            # 改进图像数量统计
            improved_count = sum(1 for d in diffs if d > 0)
            degraded_count = sum(1 for d in diffs if d < 0)
            unchanged_count = sum(1 for d in diffs if d == 0)

            f.write(f"\nPerformance change statistics:\n")
            f.write(f"  Improved images: {improved_count} ({improved_count/total_images*100:.1f}%)\n")
            f.write(f"  Degraded images: {degraded_count} ({degraded_count/total_images*100:.1f}%)\n")
            f.write(f"  Unchanged images: {unchanged_count} ({unchanged_count/total_images*100:.1f}%)\n")

            f.write(f"\nDetailed results (sorted by difference):\n")
            f.write(f"{'Rank':<6} {'Image Name':<15} {'Baseline':<12} {'Current':<12} {'Difference':<12}\n")
            f.write(f"{'-'*65}\n")

            for result in results:
                f.write(f"{result['rank']:<6} {result['image_name']:<15} "
                       f"{result['baseline_value']:<12.6f} {result['current_value']:<12.6f} "
                       f"{result['diff_value']:<12.6f}\n")

def create_statistics_analysis(output_dir, all_results, metrics):
    """创建统计分析图表"""
    try:
        import matplotlib.pyplot as plt

        # 设置matplotlib使用英文，避免中文字体问题
        plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建统计图表目录
        stats_dir = os.path.join(output_dir, "statistics")
        os.makedirs(stats_dir, exist_ok=True)

        for metric in metrics:
            results = all_results[metric]
            diffs = [r['diff_value'] for r in results]
            baseline_values = [r['baseline_value'] for r in results]
            current_values = [r['current_value'] for r in results]

            # 创建差异分布直方图
            plt.figure(figsize=(12, 8))

            plt.subplot(2, 2, 1)
            plt.hist(diffs, bins=30, alpha=0.7, color='blue', edgecolor='black')
            plt.title(f'{metric} Difference Distribution (vs GT)')
            plt.xlabel('Difference Value')
            plt.ylabel('Number of Images')
            plt.axvline(x=0, color='red', linestyle='--', label='No Difference Line')
            plt.legend()

            # 创建基准值vs当前值散点图
            plt.subplot(2, 2, 2)
            plt.scatter(baseline_values, current_values, alpha=0.6)
            plt.plot([min(baseline_values), max(baseline_values)],
                    [min(baseline_values), max(baseline_values)],
                    'r--', label='y=x (No Change Line)')
            plt.xlabel('Baseline Value')
            plt.ylabel('Current Value')
            plt.title(f'{metric} Baseline vs Current')
            plt.legend()

            # 创建差异排序图
            plt.subplot(2, 2, 3)
            sorted_diffs = sorted(diffs, reverse=True)
            plt.plot(range(len(sorted_diffs)), sorted_diffs, 'b-', linewidth=2)
            plt.xlabel('Image Rank')
            plt.ylabel('Difference Value')
            plt.title(f'{metric} Difference Ranking (vs GT)')
            plt.axhline(y=0, color='red', linestyle='--', alpha=0.7)

            # 创建累积分布图
            plt.subplot(2, 2, 4)
            sorted_diffs_for_cdf = sorted(diffs)
            y_vals = np.arange(1, len(sorted_diffs_for_cdf) + 1) / len(sorted_diffs_for_cdf)
            plt.plot(sorted_diffs_for_cdf, y_vals, 'g-', linewidth=2)
            plt.xlabel('Difference Value')
            plt.ylabel('Cumulative Probability')
            plt.title(f'{metric} Difference CDF (vs GT)')
            plt.axvline(x=0, color='red', linestyle='--', alpha=0.7)

            plt.tight_layout()
            plt.savefig(os.path.join(stats_dir, f'{metric}_analysis_withgt.png'), dpi=300, bbox_inches='tight')
            plt.close()

        print(f"Statistical analysis charts saved to {stats_dir}")

    except ImportError:
        print("matplotlib not installed, skipping chart generation")
    except Exception as e:
        print(f"Error generating statistical charts: {str(e)}")

if __name__ == "__main__":
    parser = ArgumentParser(description="Compare all rendering results with ground truth visualization")
    parser.add_argument("--baseline_dir", default="output/bicycle/imp_score_opa_origin", help="Baseline folder path")
    parser.add_argument("--current_dir", default="output/bicycle/_imp_score_cull_sh_1200_15000_0.6_25200_35200_0.62_30000", help="Current folder path")
    parser.add_argument("--output_dir", default="output/bicycle/_imp_score_cull_sh_1200_15000_0.6_25200_35200_0.62_30000/comparison_withgt_all", help="Output folder path")

    args = parser.parse_args()

    process_comparison_all(args.baseline_dir, args.current_dir, args.output_dir)
