#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON>retta<PERSON>@inria.fr
#

import os
import json
import torch
import numpy as np
from PIL import Image
import torchvision.transforms.functional as tf
import torchvision
import matplotlib.pyplot as plt
from pathlib import Path
from argparse import ArgumentParser
from tqdm import tqdm
import shutil

def load_json(json_path):
    """加载JSON文件"""
    with open(json_path, 'r') as f:
        return json.load(f)

def find_top_diff_images(baseline_data, current_data, metric, top_n=3):
    """找出指定指标差异最大的前N张图像"""
    diff_dict = {}
    
    # 确保我们比较的是相同的图像集
    baseline_images = set(baseline_data["test"]["ours_30000"][metric].keys())
    current_images = set(current_data["ours_50000"][metric].keys())
    common_images = baseline_images.intersection(current_images)
    
    if not common_images:
        raise ValueError(f"没有找到共同的图像来比较 {metric} 指标")
    
    # 计算每张图像的指标差异
    for img_name in common_images:
        baseline_value = baseline_data["test"]["ours_30000"][metric][img_name]
        current_value = current_data["ours_50000"][metric][img_name]
        
        # 对于LPIPS，较小的值更好；对于SSIM和PSNR，较大的值更好
        if metric == "LPIPS":
            diff = baseline_value - current_value  # 正值表示当前方法更好
        else:
            diff = current_value - baseline_value  # 正值表示当前方法更好
        
        diff_dict[img_name] = diff
    
    # 按差异排序并返回前N个（差异最大的）
    sorted_diffs = sorted(diff_dict.items(), key=lambda x: x[1], reverse=True)
    return sorted_diffs[:top_n]

# def load_image(image_path):
#     """加载图像并转换为PyTorch张量"""
#     if not os.path.exists(image_path):
#         raise FileNotFoundError(f"找不到图像: {image_path}")
    
#     # 尝试加载PNG图像
#     if image_path.endswith('.png'):
#         img = Image.open(image_path).convert("RGB")
#         return tf.to_tensor(img)
    
#     # 尝试加载NPY文件
#     elif image_path.endswith('.npy'):
#         img_np = np.load(image_path)
#         # 如果是HWC格式，转换为CHW
#         if img_np.shape[-1] == 3:
#             img_np = np.transpose(img_np, (2, 0, 1))
#         return torch.from_numpy(img_np).float()
    
#     else:
#         raise ValueError(f"不支持的图像格式: {image_path}")

def load_image(image_path, target_size=None):
    """加载图像并转换为PyTorch张量"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"找不到图像: {image_path}")
    
    # 尝试加载PNG图像
    if image_path.endswith('.png'):
        img = Image.open(image_path).convert("RGB")
        tensor = tf.to_tensor(img)
    # 尝试加载NPY文件
    elif image_path.endswith('.npy'):
        img_np = np.load(image_path)
        # 如果是HWC格式，转换为CHW
        if img_np.shape[-1] == 3:
            img_np = np.transpose(img_np, (2, 0, 1))
        tensor = torch.from_numpy(img_np).float()
    else:
        raise ValueError(f"不支持的图像格式: {image_path}")
    
    # 如果指定了目标尺寸，进行resize
    if target_size is not None:
        tensor = tf.resize(tensor, target_size)
    
    return tensor

def create_diff_image(img1, img2):
    """创建两个图像之间的差异图"""
    # 计算绝对差异
    diff = torch.abs(img1 - img2).mean(dim=0)
    
    # 归一化差异以便更好地可视化
    diff = diff - diff.min() / (diff.max() - diff.min() + 1e-8)
    
    # 使用热力图颜色映射
    # 红色表示差异大，蓝色表示差异小
    r = torch.clamp(diff * 2, 0, 1)
    g = torch.clamp(2 - diff * 4, 0, 1) * torch.clamp(diff * 4, 0, 1)
    b = torch.clamp(1 - diff * 2, 0, 1)
    
    return torch.stack([r, g, b], dim=0)

def process_comparison(baseline_dir, current_dir, output_dir, top_n=3):
    """处理两个文件夹之间的比较并生成可视化结果"""
    # 加载per_view.json文件
    baseline_json_path = os.path.join(baseline_dir, "per_view.json")
    current_json_path = os.path.join(current_dir, "per_view.json")
    
    if not os.path.exists(baseline_json_path):
        raise FileNotFoundError(f"找不到基准文件: {baseline_json_path}")
    if not os.path.exists(current_json_path):
        raise FileNotFoundError(f"找不到当前文件: {current_json_path}")
    
    baseline_data = load_json(baseline_json_path)
    current_data = load_json(current_json_path)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 对每个指标找出差异最大的图像
    metrics = ["SSIM", "PSNR", "LPIPS"]
    all_results = {}
    
    for metric in metrics:
        print(f"处理 {metric} 指标...")
        top_diff_images = find_top_diff_images(baseline_data, current_data, metric, top_n)
        
        # 创建指标子目录
        metric_dir = os.path.join(output_dir, metric)
        os.makedirs(metric_dir, exist_ok=True)
        
        all_results[metric] = []
        
        for i, (img_name, diff_value) in enumerate(top_diff_images):
            img_base_name = os.path.splitext(img_name)[0]
            
            # 创建图像子目录
            img_dir = os.path.join(metric_dir, f"{i+1}_{img_base_name}")
            os.makedirs(img_dir, exist_ok=True)
            
            # 加载图像
            try:
                # 基准渲染图
                baseline_render_path = os.path.join(baseline_dir, "test", "ours_30000", "renders", img_name)
                baseline_render = load_image(baseline_render_path)
                target_size = baseline_render.shape[1:] 
                
                # 当前渲染图
                current_render_path = os.path.join(current_dir, "test", "ours_50000", "renders", img_name)
                current_render = load_image(current_render_path, target_size)
                
                # 真值图
                gt_path = os.path.join(current_dir, "test", "ours_50000", "gt", img_name)
                gt_image = load_image(gt_path, target_size)
                
                # 创建差异图
                diff_image = create_diff_image(baseline_render, current_render)
                
                # 保存图像
                torchvision.utils.save_image(baseline_render, os.path.join(img_dir, "baseline.png"))
                torchvision.utils.save_image(current_render, os.path.join(img_dir, "current.png"))
                torchvision.utils.save_image(gt_image, os.path.join(img_dir, "gt.png"))
                torchvision.utils.save_image(diff_image, os.path.join(img_dir, "diff.png"))
                
                # 创建组合图像以便比较
                combined = torch.cat([
                    torch.cat([baseline_render, current_render], dim=2),
                    torch.cat([gt_image, diff_image], dim=2)
                ], dim=1)
                
                torchvision.utils.save_image(combined, os.path.join(img_dir, "combined.png"))
                
                # 记录结果
                baseline_value = baseline_data["test"]["ours_30000"][metric][img_name]
                current_value = current_data["ours_50000"][metric][img_name]
                
                result = {
                    "image_name": img_name,
                    "baseline_value": baseline_value,
                    "current_value": current_value,
                    "diff_value": diff_value,
                    "output_dir": img_dir
                }
                all_results[metric].append(result)
                
                # 创建信息文本文件
                with open(os.path.join(img_dir, "info.txt"), "w") as f:
                    f.write(f"图像: {img_name}\n")
                    f.write(f"基准 {metric}: {baseline_value:.6f}\n")
                    f.write(f"当前 {metric}: {current_value:.6f}\n")
                    f.write(f"差异: {diff_value:.6f}\n")
                    if metric == "LPIPS":
                        f.write("注意: LPIPS值越小越好\n")
                    else:
                        f.write(f"注意: {metric}值越大越好\n")
                
                print(f"  {img_name}: 基准={baseline_value:.6f}, 当前={current_value:.6f}, 差异={diff_value:.6f}")
                
            except Exception as e:
                print(f"处理图像 {img_name} 时出错: {str(e)}")
    
    # 创建总结报告
    with open(os.path.join(output_dir, "summary.txt"), "w") as f:
        f.write(f"基准目录: {baseline_dir}\n")
        f.write(f"当前目录: {current_dir}\n\n")
        
        for metric in metrics:
            f.write(f"\n{metric} 指标差异最大的前 {top_n} 张图像:\n")
            for result in all_results[metric]:
                f.write(f"  {result['image_name']}: 基准={result['baseline_value']:.6f}, 当前={result['current_value']:.6f}, 差异={result['diff_value']:.6f}\n")
    
    print(f"\n比较完成。结果保存在 {output_dir}")
    return all_results

if __name__ == "__main__":
    parser = ArgumentParser(description="比较两个文件夹中的渲染结果并生成可视化")
    parser.add_argument("--baseline_dir", default="../gaussian-splatting/output/bicycle_1200_eval", help="基准文件夹路径")
    # parser.add_argument("--baseline_dir", default="output/bicycle_f1d16h32_hier_3", help="基准文件夹路径")
    parser.add_argument("--current_dir", default="output/bicycle/imp_score_opa_origin" ,help="当前文件夹路径")
    parser.add_argument("--output_dir", default="output/bicycle/imp_score_opa_origin/comparison_wo_distill", help="输出文件夹路径")
    parser.add_argument("--top_n", type=int, default=3, help="每个指标要显示的差异最大的图像数量")
    
    args = parser.parse_args()
    
    process_comparison(args.baseline_dir, args.current_dir, args.output_dir, args.top_n)
