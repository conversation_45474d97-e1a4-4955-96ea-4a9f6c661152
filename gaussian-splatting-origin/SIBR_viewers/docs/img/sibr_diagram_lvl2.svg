<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="771px" height="1131px" version="1.1" content="&lt;mxfile userAgent=&quot;Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36&quot; version=&quot;6.7.2&quot; editor=&quot;www.draw.io&quot; type=&quot;google&quot;&gt;&lt;diagram name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-f7f7f7-1-e0e0e0-1-s-0"><stop offset="0%" style="stop-color:#F7F7F7"/><stop offset="100%" style="stop-color:#E0E0E0"/></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-f9f7fc-1-b3bce0-1-s-0"><stop offset="0%" style="stop-color:#F9F7FC"/><stop offset="100%" style="stop-color:#B3BCE0"/></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-faeded-1-eabbbb-1-s-0"><stop offset="0%" style="stop-color:#FAEDED"/><stop offset="100%" style="stop-color:#EABBBB"/></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-cce5ff-1-ffffff-1-s-0"><stop offset="0%" style="stop-color:#CCE5FF"/><stop offset="100%" style="stop-color:#ffffff"/></linearGradient><linearGradient x1="0%" y1="0%" x2="0%" y2="100%" id="mx-gradient-f5faff-1-ffffff-1-s-0"><stop offset="0%" style="stop-color:#F5FAFF"/><stop offset="100%" style="stop-color:#ffffff"/></linearGradient></defs><g transform="translate(0.5,0.5)"><rect x="0" y="250" width="440" height="220" fill="#f5faff" stroke="#000000" pointer-events="none"/><g transform="translate(172.5,262.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="94" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 95px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Renderer Libs</div></div></foreignObject><text x="47" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">Renderer Libs</text></switch></g><rect x="460" y="110" width="310" height="360" fill="#f5faff" stroke="#000000" pointer-events="none"/><g transform="translate(589.5,122.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 51px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Utilities</div></div></foreignObject><text x="25" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">Utilities</text></switch></g><rect x="0" y="970" width="770" height="160" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(343.5,982.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="82" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 83px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_system</div></div></foreignObject><text x="41" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_system</text></switch></g><rect x="0" y="490" width="280" height="240" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(101.5,502.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="77" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 78px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_assets</div></div></foreignObject><text x="39" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_assets</text></switch></g><rect x="0" y="750" width="770" height="200" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(338.5,762.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 93px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_graphics</div></div></foreignObject><text x="46" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_graphics</text></switch></g><rect x="230" y="290" width="190" height="130" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(271.5,302.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_spixelwarp<br /></div></div></foreignObject><text x="53" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_spixelwarp&lt;br&gt;</text></switch></g><rect x="240.04" y="340" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(258.5,343.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="42" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 43px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">SWARP<br />Renderer</div></div></foreignObject><text x="21" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">SWARP&lt;br&gt;Renderer</text></switch></g><rect x="241.04" y="380" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(271.5,389.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="19" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 20px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Utils</div></div></foreignObject><text x="10" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Utils</text></switch></g><rect x="20" y="290" width="190" height="120" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(33.5,302.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="162" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 163px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_renderer (common)<br /></div></div></foreignObject><text x="81" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_renderer (common)&lt;br&gt;</text></switch></g><rect x="20" y="420" width="190" height="40" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(88.5,432.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="52" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 52px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_url<br /></div></div></foreignObject><text x="26" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_url&lt;br&gt;</text></switch></g><rect x="231" y="430" width="190" height="30" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(320.5,442.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="11" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 13px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">...</div></div></foreignObject><text x="6" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">...</text></switch></g><rect x="30" y="1090" width="350" height="30" fill="url(#mx-gradient-f9f7fc-1-b3bce0-1-s-0)" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(189.5,1098.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="30" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 31px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Boost</div></div></foreignObject><text x="15" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Boost</text></switch></g><rect x="120" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(141.5,1019.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="37" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 38px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Property</div></div></foreignObject><text x="19" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Property</text></switch></g><rect x="30" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(55.5,1019.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="29" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 30px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Config</div></div></foreignObject><text x="15" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Config</text></switch></g><rect x="300" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(328.5,1019.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="23" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 24px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">(Log)</div></div></foreignObject><text x="12" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">(Log)</text></switch></g><rect x="210" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(220.5,1019.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="58" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 59px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">ProgramArgs</div></div></foreignObject><text x="29" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">ProgramArgs</text></switch></g><rect x="480" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(500.5,1013.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="39" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Loading<br />Progress</div></div></foreignObject><text x="20" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Loading&lt;br&gt;Progress</text></switch></g><rect x="390" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(404.5,1019.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 51px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">ByteStream</div></div></foreignObject><text x="25" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">ByteStream</text></switch></g><rect x="660" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="570" y="1010" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(592.5,1019.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Array2d<br /></div></div></foreignObject><text x="17" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Array2d&lt;br&gt;</text></switch></g><rect x="390" y="1090" width="350" height="30" fill="url(#mx-gradient-f9f7fc-1-b3bce0-1-s-0)" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(508.5,1098.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="113" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 114px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">C++ Standard Library<br /></div></div></foreignObject><text x="57" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">C++ Standard Library&lt;br&gt;</text></switch></g><rect x="121.04" y="330" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(128.5,339.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="65" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 66px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">CopyRenderer</div></div></foreignObject><text x="33" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">CopyRenderer</text></switch></g><rect x="31.04" y="330" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(41.5,339.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="59" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 60px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">BlurRenderer</div></div></foreignObject><text x="30" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">BlurRenderer</text></switch></g><rect x="120" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(132.5,879.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="54" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 55px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderView</div></div></foreignObject><text x="27" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">RenderView</text></switch></g><rect x="30" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(42.5,873.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="55" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 56px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">ImageRGB/<br />ImageRGBA<br /></div></div></foreignObject><text x="28" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">ImageRGB/&lt;br&gt;ImageRGBA&lt;br&gt;</text></switch></g><rect x="300" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(322.5,879.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Vector3</div></div></foreignObject><text x="17" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Vector3</text></switch></g><rect x="210" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(233.5,879.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="32" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Matrix4</div></div></foreignObject><text x="16" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Matrix4</text></switch></g><rect x="480" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(494.5,879.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 51px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Quaternion</div></div></foreignObject><text x="25" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Quaternion</text></switch></g><rect x="390" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(412.5,879.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 35px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Vector2</div></div></foreignObject><text x="17" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Vector2</text></switch></g><rect x="660" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="570" y="870" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="120" y="830" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(137.5,839.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="45" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PixelMask</div></div></foreignObject><text x="23" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PixelMask</text></switch></g><rect x="30" y="830" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="100" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(105.5,659.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="68" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 69px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PixelSpixelssss</div></div></foreignObject><text x="34" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PixelSpixelssss</text></switch></g><rect x="210" y="830" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(222.5,839.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="54" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 55px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PixelNormal</div></div></foreignObject><text x="27" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PixelNormal</text></switch></g><rect x="390" y="830" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(402.5,839.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="55" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 57px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Transform3d</div></div></foreignObject><text x="28" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Transform3d</text></switch></g><rect x="660" y="830" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(669.5,839.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="61" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderTarget</div></div></foreignObject><text x="31" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">RenderTarget</text></switch></g><rect x="570" y="830" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(593.5,839.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="33" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 33px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Texture</div></div></foreignObject><text x="17" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Texture</text></switch></g><rect x="120" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(147.5,799.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="24" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 25px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Mesh</div></div></foreignObject><text x="12" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Mesh</text></switch></g><rect x="30" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(56.5,799.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="27" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Image</div></div></foreignObject><text x="14" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Image</text></switch></g><rect x="300" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(320.5,799.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="38" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 39px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Viewport</div></div></foreignObject><text x="19" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Viewport</text></switch></g><rect x="210" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(231.5,799.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="36" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 37px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Window</div></div></foreignObject><text x="18" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Window</text></switch></g><rect x="390" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(412.5,799.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="35" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 36px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Camera</div></div></foreignObject><text x="18" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Camera</text></switch></g><rect x="660" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(667.5,793.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 65px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderUtility<br /><font style="font-size: 7px">(additional functions)</font><br /></div></div></foreignObject><text x="32" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="570" y="790" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(573.5,793.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="72" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 73px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Renderer<font style="font-size: 6px"><br />(OpenGL States/Functions)</font><br /></div></div></foreignObject><text x="36" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="670" y="490" width="100" height="240" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(704.5,502.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="30" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 31px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 11px">Other</font></div></div></foreignObject><text x="15" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">[Not supported by viewer]</text></switch></g><rect x="100" y="690" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="10" y="690" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="190" y="690" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="10" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(19.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="61" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Property Keys<br /></div></div></foreignObject><text x="31" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Property Keys&lt;br&gt;</text></switch></g><rect x="190" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="100" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(120.5,619.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="38" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 39px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">MVSFile</div></div></foreignObject><text x="19" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">MVSFile</text></switch></g><rect x="10" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(17.5,619.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 65px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">ActiveCamFile</div></div></foreignObject><text x="32" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">ActiveCamFile</text></switch></g><rect x="190" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(204.5,619.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 51px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">SDepthFile</div></div></foreignObject><text x="25" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">SDepthFile</text></switch></g><rect x="100" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(115.5,579.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="48" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 49px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">QualityFile</div></div></foreignObject><text x="24" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">QualityFile</text></switch></g><rect x="10" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(28.5,579.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="42" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 43px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PlaneFile</div></div></foreignObject><text x="21" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PlaneFile</text></switch></g><rect x="190" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(200.5,579.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="59" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 60px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">ImageListFile</div></div></foreignObject><text x="30" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">ImageListFile</text></switch></g><rect x="290" y="490" width="370" height="200" fill="url(#mx-gradient-f7f7f7-1-e0e0e0-1-s-0)" stroke="#666666" pointer-events="none"/><g transform="translate(442.5,502.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 65px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_view</div></div></foreignObject><text x="32" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">sibr_view</text></switch></g><rect x="390" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="300" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="570" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="480" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="390" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(401.5,613.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="57" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 58px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderView:<br />TopView<br /></div></div></foreignObject><text x="29" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">RenderView:&lt;br&gt;TopView&lt;br&gt;</text></switch></g><rect x="300" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(311.5,613.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="57" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 58px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderView:<br />SceneView<br /></div></div></foreignObject><text x="29" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">RenderView:&lt;br&gt;SceneView&lt;br&gt;</text></switch></g><rect x="570" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(579.5,613.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="60" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 60px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderView:<br />ShowRTView<br /></div></div></foreignObject><text x="30" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">RenderView:&lt;br&gt;ShowRTView&lt;br&gt;</text></switch></g><rect x="480" y="610" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(491.5,613.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="57" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 58px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderView:<br />DebugView<br /></div></div></foreignObject><text x="29" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">RenderView:&lt;br&gt;DebugView&lt;br&gt;</text></switch></g><rect x="390" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(397.5,575.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="19" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 65px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderingMode:<br />Mono<br /></div></div></foreignObject><text x="32" y="14" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica">RenderingMode:&lt;br&gt;Mono&lt;br&gt;</text></switch></g><rect x="300" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(315.5,579.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="49" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 50px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">UIBehavior</div></div></foreignObject><text x="25" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">UIBehavior</text></switch></g><rect x="570" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(573.5,573.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="72" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 73px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 9px">RenderingMode:<br />StereoQuadBuffer</font></div></div></foreignObject><text x="36" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="480" y="570" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(486.5,575.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="19" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 67px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">RenderingMode:<br />Stereo Anaglyph<br /></div></div></foreignObject><text x="33" y="14" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="390" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(400.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="58" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 59px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">CameraDolly</div></div></foreignObject><text x="29" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">CameraDolly</text></switch></g><rect x="300" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(321.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="36" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 37px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">IBRArgs</div></div></foreignObject><text x="18" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">IBRArgs</text></switch></g><rect x="570" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(589.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="41" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 41px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Trackball</div></div></foreignObject><text x="21" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">Trackball</text></switch></g><rect x="480" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(489.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="61" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PathRecorder</div></div></foreignObject><text x="31" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PathRecorder</text></switch></g><rect x="120" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="30" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="300" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="210" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="480" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="390" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="660" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="570" y="1050" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="100" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(118.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="42" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 43px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PatchFile</div></div></foreignObject><text x="21" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PatchFile</text></switch></g><rect x="10" y="650" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(23.5,653.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="52" height="22" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 53px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">CameraFile<br /><font style="font-size: 8px">(BundleOut)</font><br /></div></div></foreignObject><text x="26" y="16" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="190" y="530" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(207.5,539.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="44" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 45px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">SpixelFile</div></div></foreignObject><text x="22" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">SpixelFile</text></switch></g><rect x="680" y="635" width="80" height="30" fill="#f5f5f5" stroke="#666666" pointer-events="none"/><g transform="translate(696.5,643.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 47px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 10px"><b>MVIIR libs</b><br /></font></div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="210" y="910" width="170" height="30" fill="url(#mx-gradient-faeded-1-eabbbb-1-s-0)" stroke="#b85450" pointer-events="none"/><g transform="translate(277.5,918.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="35" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 36px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">GLEW<br /></div></div></foreignObject><text x="18" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">GLEW&lt;br&gt;</text></switch></g><rect x="390" y="910" width="170" height="30" fill="url(#mx-gradient-f9f7fc-1-b3bce0-1-s-0)" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(451.5,918.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 47px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">OpenCV</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">OpenCV</text></switch></g><rect x="570" y="910" width="170" height="30" fill="url(#mx-gradient-f9f7fc-1-b3bce0-1-s-0)" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(612.5,911.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="84" height="27" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 85px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 11px">Eigen<font style="font-size: 2px"></font></font><font style="font-size: 2px"><br /></font><font style="font-size: 6px"><font style="font-size: 2px"></font>(might be moved in IBR System)</font><br /></div></div></foreignObject><text x="42" y="20" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="30" y="910" width="170" height="30" fill="url(#mx-gradient-faeded-1-eabbbb-1-s-0)" stroke="#b85450" pointer-events="none"/><g transform="translate(92.5,918.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="45" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">OpenGL<br /></div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">OpenGL&lt;br&gt;</text></switch></g><rect x="620" y="370" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(636.5,384.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="107" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">img_normal.exe</div></div></foreignObject><text x="54" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">img_normal.exe</text></switch></g><rect x="470" y="370" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(489.5,384.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="101" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 101px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">maskpatch.exe</div></div></foreignObject><text x="51" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">maskpatch.exe</text></switch></g><rect x="620" y="320" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(627.5,334.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="125" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 125px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">color_harmoni.exe</div></div></foreignObject><text x="63" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">color_harmoni.exe</text></switch></g><rect x="470" y="320" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(484.5,334.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="110" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 111px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">qualityEstim.exe</div></div></foreignObject><text x="55" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">qualityEstim.exe</text></switch></g><rect x="620" y="270" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(638.5,284.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="102" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 103px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">planeEstim.exe<br /></div></div></foreignObject><text x="51" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">planeEstim.exe&lt;br&gt;</text></switch></g><rect x="470" y="270" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(483.5,284.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="112" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 113px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">scene2patch.exe</div></div></foreignObject><text x="56" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">scene2patch.exe</text></switch></g><rect x="620" y="220" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(624.5,234.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="131" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 133px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">depthSynthesis.exe</div></div></foreignObject><text x="66" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">depthSynthesis.exe</text></switch></g><rect x="470" y="220" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(490.5,234.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="98" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 98px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">superpixel.exe</div></div></foreignObject><text x="49" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">superpixel.exe</text></switch></g><rect x="620" y="170" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(645.5,184.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="88" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 89px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">undistort.exe</div></div></foreignObject><text x="44" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">undistort.exe</text></switch></g><rect x="470" y="170" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(506.5,184.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 66px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">depth.exe</div></div></foreignObject><text x="33" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">depth.exe</text></switch></g><rect x="620" y="420" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="470" y="420" width="140" height="40" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="0" y="0" width="770" height="50" fill="url(#mx-gradient-cce5ff-1-ffffff-1-s-0)" stroke="#000000" pointer-events="none"/><g transform="translate(349.5,7.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="71" height="34" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 73px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 30px"><b>SIBR</b></font></div></div></foreignObject><text x="36" y="23" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="0" y="60" width="440" height="180" fill="#f5faff" stroke="#000000" pointer-events="none"/><g transform="translate(169.5,72.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="100" height="15" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 102px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Renderer Apps</div></div></foreignObject><text x="50" y="15" fill="#000000" text-anchor="middle" font-size="14px" font-family="Helvetica" font-weight="bold">Renderer Apps</text></switch></g><rect x="161" y="143" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(180.5,156.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="80" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 81px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_fplan_app.exe</div></div></foreignObject><text x="40" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_fplan_app.exe</text></switch></g><rect x="21" y="143" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(32.5,156.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="97" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 98px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_selection_app.exe</div></div></foreignObject><text x="49" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_selection_app.exe</text></switch></g><rect x="161" y="103" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(185.5,116.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="70" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 71px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_ulr_app.exe</div></div></foreignObject><text x="35" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_ulr_app.exe</text></switch></g><rect x="21" y="103" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(39.5,116.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="82" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 83px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_proxy_app.exe</div></div></foreignObject><text x="41" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_proxy_app.exe</text></switch></g><rect x="301" y="143" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(313.5,156.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="95" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 96px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><span>sibr_compare_app.exe</span></div></div></foreignObject><text x="48" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">&lt;span&gt;sibr_compare_app.exe&lt;/span&gt;</text></switch></g><rect x="301" y="103" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(308.5,116.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="104" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 105px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_spixelwarp_app.exe</div></div></foreignObject><text x="52" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_spixelwarp_app.exe</text></switch></g><rect x="330.04" y="340" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(342.5,349.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="54" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 55px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">SpixelImage</div></div></foreignObject><text x="27" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">SpixelImage</text></switch></g><rect x="330.04" y="380" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(366.5,389.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="7" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 8px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">...</div></div></foreignObject><text x="4" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">...</text></switch></g><path d="M 290 700 L 643 700 L 660 717 L 660 740 L 290 740 L 290 700 Z" fill="url(#mx-gradient-f5faff-1-ffffff-1-s-0)" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 643 700 L 643 717 L 660 717" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(324.5,715.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="300" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 301px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">TODO: sibr_view should be higher (always) than sibr_assets in the hierarchy<br /></div></div></foreignObject><text x="150" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica">TODO: sibr_view should be higher (always) than sibr_assets in the hierarchy&lt;br&gt;</text></switch></g><rect x="161" y="195" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(173.5,208.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="94" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 95px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_mixed_model.exe</div></div></foreignObject><text x="47" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_mixed_model.exe</text></switch></g><rect x="21" y="195" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(42.5,208.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="77" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 78px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">sibr_plan_app.exe</div></div></foreignObject><text x="39" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">sibr_plan_app.exe</text></switch></g><rect x="301" y="195" width="120" height="30" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(313.5,208.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="95" height="8" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 9px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 96px; white-space: nowrap; word-wrap: normal; font-weight: bold; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><span>sibr_compare_app.exe</span></div></div></foreignObject><text x="48" y="9" fill="#000000" text-anchor="middle" font-size="9px" font-family="Helvetica" font-weight="bold">&lt;span&gt;sibr_compare_app.exe&lt;/span&gt;</text></switch></g><path d="M 160 720 L 160 720" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 160 720 L 160 720 L 160 720 L 160 720 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="10" y="690" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(21.5,699.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="57" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 58px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">InputCamera</div></div></foreignObject><text x="29" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">InputCamera</text></switch></g><rect x="100" y="690" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(112.5,699.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="55" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 56px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">SpixelGraph</div></div></foreignObject><text x="28" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">SpixelGraph</text></switch></g><rect x="680" y="530" width="80" height="30" fill="#f5f5f5" stroke="#666666" pointer-events="none"/><g transform="translate(693.5,538.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="52" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 53px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><span style="font-size: 10px"><b>Raytracing</b></span></div></div></foreignObject><text x="26" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="680" y="566" width="80" height="30" fill="#f5f5f5" stroke="#666666" pointer-events="none"/><g transform="translate(704.5,574.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="30" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 31px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 10px"><b>Vision</b><br /></font></div></div></foreignObject><text x="15" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="680" y="600" width="80" height="30" fill="#f5f5f5" stroke="#666666" pointer-events="none"/><g transform="translate(699.5,608.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 41px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 10px"><b>ImgProc</b><br /></font></div></div></foreignObject><text x="20" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="680" y="670" width="80" height="30" fill="#f5f5f5" stroke="#666666" pointer-events="none"/><g transform="translate(716.5,678.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="7" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 8px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font style="font-size: 10px"><b>...</b><br /></font></div></div></foreignObject><text x="4" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="120.04" y="370" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(156.5,379.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="7" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 8px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">...</div></div></foreignObject><text x="4" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">...</text></switch></g><rect x="30.04" y="370" width="80" height="30" rx="4.5" ry="4.5" fill="#ffffff" stroke="#000000" pointer-events="none"/><g transform="translate(31.5,379.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="77" height="10" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 78px; white-space: nowrap; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">PoissonRenderer</div></div></foreignObject><text x="39" y="10" fill="#000000" text-anchor="middle" font-size="10px" font-family="Helvetica">PoissonRenderer</text></switch></g></g></svg>