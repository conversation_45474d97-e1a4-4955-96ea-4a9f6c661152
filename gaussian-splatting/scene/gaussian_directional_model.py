#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

import torch
import numpy as np
from utils.general_utils import inverse_sigmoid, get_expon_lr_func, build_rotation
from torch import nn
import os
from utils.system_utils import mkdir_p
from plyfile import PlyData, PlyElement
from utils.sh_utils import RGB2SH
from simple_knn._C import distCUDA2
from utils.graphics_utils import BasicPointCloud
from utils.general_utils import strip_symmetric, build_scaling_rotation
from scene.gaussian_model import GaussianModel

class GaussianDirectionalModel(GaussianModel):
    def __init__(self, sh_degree: int, use_ray=False, optimizer_type="default"):
        self.active_sh_degree = 0
        self.optimizer_type = optimizer_type
        self.max_sh_degree = sh_degree  
        self._xyz = torch.empty(0)
        # self._features_dc = torch.empty(0)          # 球谐系数DC分量（SH0）
        # self._features_rest = torch.empty(0)        # 高阶球谐系数（SH1~SHn）
        self._rgb = torch.empty(0)
        self._scaling = torch.empty(0)
        self._rotation = torch.empty(0)
        self._opacity = torch.empty(0)
        self.max_radii2D = torch.empty(0)           # 各高斯在2D投影的最大半径
        self.xyz_gradient_accum = torch.empty(0)
        self.denom = torch.empty(0)
        self.optimizer = None
        self.percent_dense = 0
        self.spatial_lr_scale = 0
        self.setup_functions()
        # Initialize direction vectors and sharpness values
        self._direction = torch.empty(0)  # Direction vector (will be normalized)
        self._sharpness = torch.empty(0)  # Sharpness value (lambda in the formula)

    def capture(self, include_feature=False):
        base_capture = super().capture(include_feature)
        # Add direction and sharpness to the captured state
        if isinstance(base_capture, tuple):
            return base_capture + (self._direction, self._sharpness)
        else:
            return base_capture

    def restore(self, model_args, training_args, mode='train'):
        """Restore model state from saved checkpoint"""
        # Check if we have direction and sharpness in the model_args
        if len(model_args) >= 14:  # Base model (12) + direction + sharpness
            # Extract direction and sharpness from the end of model_args
            direction = model_args[-2]
            sharpness = model_args[-1]
            
            # Restore base model without direction and sharpness
            super().restore(model_args[:-2], training_args, mode)
            
            # Set direction and sharpness
            self._direction = direction
            self._sharpness = sharpness
        else:
            # Restore base model
            super().restore(model_args, training_args, mode)
            
            # Initialize direction and sharpness if they don't exist
            if self._direction.shape[0] == 0 or self._direction.shape[0] != self._xyz.shape[0]:
                # Initialize random directions (will be normalized)
                direction = torch.randn((self._xyz.shape[0], 3), device="cuda")
                # Normalize directions
                direction = direction / (torch.norm(direction, dim=1, keepdim=True) + 1e-8)
                self._direction = nn.Parameter(direction.requires_grad_(True))
                
                # Initialize sharpness values (controls how directional the feature is)
                # Higher values make features more directional
                
                # sharpness = torch.ones((self._xyz.shape[0], 1), device="cuda") * 5.0
                sharpness = torch.ones((self._xyz.shape[0], 1), device="cuda") * 0.1
                self._sharpness = nn.Parameter(sharpness.requires_grad_(True))

    @property
    def get_direction(self):
        """Get normalized direction vectors"""
        return torch.nn.functional.normalize(self._direction, dim=1)
    
    @property
    def get_sharpness(self):
        """Get sharpness values (positive only)"""
        return torch.abs(self._sharpness)  # Ensure sharpness is positive

    def training_setup(self, training_args):
        """Setup training parameters including direction and sharpness"""
        # Initialize direction and sharpness if they don't exist
        if self._direction.shape[0] == 0 or self._direction.shape[0] != self._xyz.shape[0]:
            # Initialize random directions (will be normalized)
            direction = torch.randn((self._xyz.shape[0], 3), device="cuda")
            # Normalize directions
            direction = direction / (torch.norm(direction, dim=1, keepdim=True) + 1e-8)
            self._direction = nn.Parameter(direction.requires_grad_(True))
            
            # Initialize sharpness values
            sharpness = torch.ones((self._xyz.shape[0], 1), device="cuda") * 2.0
            self._sharpness = nn.Parameter(sharpness.requires_grad_(True))
        
        # Call parent training setup
        super().training_setup(training_args)
        
        # Add direction and sharpness to optimizer
        l = [
            {'params': [self._xyz], 'lr': training_args.position_lr_init * self.spatial_lr_scale, "name": "xyz"},
            {'params': [self._features_dc], 'lr': training_args.feature_lr, "name": "f_dc"},
            {'params': [self._features_rest], 'lr': training_args.feature_lr / 20.0, "name": "f_rest"},
            {'params': [self._opacity], 'lr': training_args.opacity_lr, "name": "opacity"},
            {'params': [self._scaling], 'lr': training_args.scaling_lr, "name": "scaling"},
            {'params': [self._rotation], 'lr': training_args.rotation_lr, "name": "rotation"},
            {'params': [self._direction], 'lr': training_args.feature_lr * 0.1, "name": "direction"},
            {'params': [self._sharpness], 'lr': training_args.feature_lr * 0.1, "name": "sharpness"},
        ]
        
        # Create new optimizer with updated parameter groups
        self.optimizer = torch.optim.Adam(l, lr=0.0, eps=1e-15)
        
        # Setup learning rate scheduler
        self.xyz_scheduler_args = get_expon_lr_func(
            lr_init=training_args.position_lr_init * self.spatial_lr_scale,
            lr_final=training_args.position_lr_final * self.spatial_lr_scale,
            lr_delay_mult=training_args.position_lr_delay_mult,
            max_steps=training_args.position_lr_max_steps
        )
        

    def construct_list_of_attributes(self):
        """Construct list of attributes for PLY export"""
        # Get base attributes
        l = super().construct_list_of_attributes()
        
        # Add direction and sharpness attributes
        for i in range(self._direction.shape[1]):
            l.append('dir_{}'.format(i))
        l.append('sharpness')
        
        return l

    def save_ply(self, path):
        """Save model to PLY file including direction and sharpness"""
        mkdir_p(os.path.dirname(path))

        # Get base attributes
        xyz = self._xyz.detach().cpu().numpy()
        normals = np.zeros_like(xyz)
        opacities = self._opacity.detach().cpu().numpy()
        scale = self._scaling.detach().cpu().numpy()
        rotation = self._rotation.detach().cpu().numpy()
        feature = self._feature.detach().cpu().numpy()
        
        # Get direction and sharpness
        direction = self._direction.detach().cpu().numpy()
        sharpness = self._sharpness.detach().cpu().numpy()

        # Create data type for PLY
        dtype_full = [(attribute, 'f4') for attribute in self.construct_list_of_attributes()]

        # Combine all attributes
        elements = np.empty(xyz.shape[0], dtype=dtype_full)
        attributes = np.concatenate((xyz, normals, opacities, scale, rotation, feature, direction, sharpness), axis=1)
        elements[:] = list(map(tuple, attributes))
        
        # Write PLY file
        el = PlyElement.describe(elements, 'vertex')
        PlyData([el]).write(path)

    def load_ply(self, path):
        """Load model from PLY file including direction and sharpness"""
        plydata = PlyData.read(path)

        # Load base attributes
        xyz = np.stack((np.asarray(plydata.elements[0]["x"]),
                        np.asarray(plydata.elements[0]["y"]),
                        np.asarray(plydata.elements[0]["z"])),  axis=1)
        opacities = np.asarray(plydata.elements[0]["opacity"])[..., np.newaxis]

        # Load features
        features_dc = np.zeros((xyz.shape[0], 3, 1))
        features_dc[:, 0, 0] = np.asarray(plydata.elements[0]["f_dc_0"])
        features_dc[:, 1, 0] = np.asarray(plydata.elements[0]["f_dc_1"])
        features_dc[:, 2, 0] = np.asarray(plydata.elements[0]["f_dc_2"])
        
        extra_f_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("f_rest_")]
        extra_f_names = sorted(extra_f_names, key = lambda x: int(x.split('_')[-1]))
        extra_f_names = [x for i, x in enumerate(extra_f_names) if i % (len(extra_f_names) // 3) < (self.max_sh_degree + 1) ** 2 - 1]
        assert len(extra_f_names)>=3*(self.max_sh_degree + 1) ** 2 - 3
        if len(extra_f_names) > 3*(self.max_sh_degree + 1) ** 2 - 3:
            print("warning: extra features are ignored")
            
        features_extra = np.zeros((xyz.shape[0], 3*(self.max_sh_degree + 1) ** 2 - 3))
        
        
        
        for idx, attr_name in enumerate(extra_f_names):
            features_extra[:, idx] = np.asarray(plydata.elements[0][attr_name])
                
        # Reshape (P,F*SH_coeffs) to (P, F, SH_coeffs except DC)
        features_extra = features_extra.reshape((features_extra.shape[0], 3, (self.max_sh_degree + 1) ** 2 - 1))

        # Load scales
        scale_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("scale_")]
        scale_names = sorted(scale_names, key = lambda x: int(x.split('_')[-1]))
        scales = np.zeros((xyz.shape[0], len(scale_names)))
        for idx, attr_name in enumerate(scale_names):
            scales[:, idx] = np.asarray(plydata.elements[0][attr_name])

        # Load rotations
        rot_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("rot")]
        rot_names = sorted(rot_names, key = lambda x: int(x.split('_')[-1]))
        rots = np.zeros((xyz.shape[0], len(rot_names)))
        for idx, attr_name in enumerate(rot_names):
            rots[:, idx] = np.asarray(plydata.elements[0][attr_name])
            
        # Load directions
        dir_names = [p.name for p in plydata.elements[0].properties if p.name.startswith("dir_")]
        dir_names = sorted(dir_names, key = lambda x: int(x.split('_')[-1]))
        directions = np.zeros((xyz.shape[0], len(dir_names)))
        for idx, attr_name in enumerate(dir_names):
            directions[:, idx] = np.asarray(plydata.elements[0][attr_name])
            
        # Load sharpness
        sharpness = np.asarray(plydata.elements[0]["sharpness"])[..., np.newaxis]

        # Set parameters
        self._xyz = nn.Parameter(torch.tensor(xyz, dtype=torch.float, device="cuda").requires_grad_(True))
        features_dc = np.ascontiguousarray(features_dc.transpose(0, 2, 1))  # NumPy 中转置并连续化
        self._features_dc = nn.Parameter(
                torch.tensor(features_dc, dtype=torch.float, device="cuda"),
            requires_grad=True)
        features_extra = np.ascontiguousarray(features_extra.transpose(0, 2, 1))  # NumPy 中转置并连续化
        self._features_rest = nn.Parameter(
                torch.tensor(features_extra, dtype=torch.float, device="cuda"),
            requires_grad=True)
        self._opacity = nn.Parameter(torch.tensor(opacities, dtype=torch.float, device="cuda").requires_grad_(True))
        self._scaling = nn.Parameter(torch.tensor(scales, dtype=torch.float, device="cuda").requires_grad_(True))
        self._rotation = nn.Parameter(torch.tensor(rots, dtype=torch.float, device="cuda").requires_grad_(True))
        self._direction = nn.Parameter(torch.tensor(directions, dtype=torch.float, device="cuda").requires_grad_(True))
        self._sharpness = nn.Parameter(torch.tensor(sharpness, dtype=torch.float, device="cuda").requires_grad_(True))

        self.active_sh_degree = self.max_sh_degree

    def _prune_optimizer(self, mask):
        """Prune optimizer state for removed points"""
        # Get base optimizable tensors
        optimizable_tensors = super()._prune_optimizer(mask)
        
        # Add direction and sharpness
        for group in self.optimizer.param_groups:
            if group["name"] == "direction" or group["name"] == "sharpness":
                stored_state = self.optimizer.state.get(group['params'][0], None)
                if stored_state is not None:
                    stored_state["exp_avg"] = stored_state["exp_avg"][mask]
                    stored_state["exp_avg_sq"] = stored_state["exp_avg_sq"][mask]

                    del self.optimizer.state[group['params'][0]]
                    group["params"][0] = nn.Parameter((group["params"][0][mask].requires_grad_(True)))
                    self.optimizer.state[group['params'][0]] = stored_state

                    optimizable_tensors[group["name"]] = group["params"][0]
                else:
                    group["params"][0] = nn.Parameter(group["params"][0][mask].requires_grad_(True))
                    optimizable_tensors[group["name"]] = group["params"][0]
                    
        return optimizable_tensors

    def prune_points(self, mask):
        """Prune points based on mask"""
        # Call parent method to prune base attributes
        valid_points_mask = ~mask
        optimizable_tensors = self._prune_optimizer(valid_points_mask)

        # Update base attributes
        self._xyz = optimizable_tensors["xyz"]
        self._opacity = optimizable_tensors["opacity"]
        self._scaling = optimizable_tensors["scaling"]
        self._rotation = optimizable_tensors["rotation"]
        self._features_dc = optimizable_tensors["f_dc"]
        self._features_rest = optimizable_tensors["f_rest"]
        
        # Update direction and sharpness
        self._direction = optimizable_tensors["direction"]
        self._sharpness = optimizable_tensors["sharpness"]

        # Update other attributes
        self.xyz_gradient_accum = self.xyz_gradient_accum[valid_points_mask]
        self.denom = self.denom[valid_points_mask]
        self.max_radii2D = self.max_radii2D[valid_points_mask]

    def cat_tensors_to_optimizer(self, tensors_dict):
        """Concatenate new tensors to optimizer state"""
        # Call parent method to handle base attributes
        optimizable_tensors = super().cat_tensors_to_optimizer(tensors_dict)
        return optimizable_tensors

    def densification_postfix(self, new_xyz, new_features_dc, new_features_rest, new_opacities, new_scaling, new_rotation, new_direction=None, new_sharpness=None):
        """Handle densification with direction and sharpness"""
        # Create dictionary of new tensors
        d = {
            "xyz": new_xyz,
            "f_dc": new_features_dc,
            "f_rest": new_features_rest,
            "opacity": new_opacities,
            "scaling": new_scaling,
            "rotation": new_rotation,
        }
        
        # Add direction and sharpness if provided
        if new_direction is not None:
            d["direction"] = new_direction
        if new_sharpness is not None:
            d["sharpness"] = new_sharpness
            
        # Call parent method to handle base attributes
        optimizable_tensors = self.cat_tensors_to_optimizer(d)
        
        # Update base attributes
        self._xyz = optimizable_tensors["xyz"]
        self._features_dc = optimizable_tensors["f_dc"]
        self._features_rest = optimizable_tensors["f_rest"]
        self._opacity = optimizable_tensors["opacity"]
        self._scaling = optimizable_tensors["scaling"]
        self._rotation = optimizable_tensors["rotation"]
        
        # Update direction and sharpness if provided
        if "direction" in optimizable_tensors:
            self._direction = optimizable_tensors["direction"]
        if "sharpness" in optimizable_tensors:
            self._sharpness = optimizable_tensors["sharpness"]

        # Reset other attributes
        self.xyz_gradient_accum = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
        self.denom = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
        self.max_radii2D = torch.zeros((self.get_xyz.shape[0]), device="cuda")

    def densify_and_split(self, grads, grad_threshold, scene_extent, N=2):
        """Split points for densification with direction and sharpness"""
        n_init_points = self.get_xyz.shape[0]
        # Extract points that satisfy the gradient condition
        padded_grad = torch.zeros((n_init_points), device="cuda")
        padded_grad[:grads.shape[0]] = grads.squeeze()
        selected_pts_mask = torch.where(padded_grad >= grad_threshold, True, False)
        selected_pts_mask = torch.logical_and(selected_pts_mask,
                                            torch.max(self.get_scaling, dim=1).values > self.percent_dense*scene_extent)

        # Handle base attributes
        stds = self.get_scaling[selected_pts_mask].repeat(N,1)
        means = torch.zeros((stds.size(0), 3), device="cuda")
        samples = torch.normal(mean=means, std=stds)
        rots = build_rotation(self._rotation[selected_pts_mask]).repeat(N,1,1)
        new_xyz = torch.bmm(rots, samples.unsqueeze(-1)).squeeze(-1) + self.get_xyz[selected_pts_mask].repeat(N, 1)
        new_scaling = self.scaling_inverse_activation(self.get_scaling[selected_pts_mask].repeat(N,1) / (0.8*N))
        new_rotation = self._rotation[selected_pts_mask].repeat(N,1)
        new_features_dc = self._features_dc[selected_pts_mask].repeat(N,1,1) if hasattr(self, '_features_dc') else None
        new_features_rest = self._features_rest[selected_pts_mask].repeat(N,1,1) if hasattr(self, '_features_rest') else None
        new_opacity = self._opacity[selected_pts_mask].repeat(N,1)
        
        # Handle direction and sharpness
        new_direction = self._direction[selected_pts_mask].repeat(N,1)
        new_sharpness = self._sharpness[selected_pts_mask].repeat(N,1)

        # Call densification postfix with all attributes
        if new_features_dc is not None and new_features_rest is not None:
            self.densification_postfix(new_xyz, new_features_dc, new_features_rest, new_opacity, new_scaling, new_rotation, new_direction, new_sharpness)
        else:
            # Create dictionary for cat_tensors_to_optimizer
            d = {
                "xyz": new_xyz,
                "opacity": new_opacity,
                "scaling": new_scaling,
                "rotation": new_rotation,
                "direction": new_direction,
                "sharpness": new_sharpness
            }
            
            # Call cat_tensors_to_optimizer directly
            optimizable_tensors = self.cat_tensors_to_optimizer(d)
            
            # Update attributes
            self._xyz = optimizable_tensors["xyz"]
            self._opacity = optimizable_tensors["opacity"]
            self._scaling = optimizable_tensors["scaling"]
            self._rotation = optimizable_tensors["rotation"]
            self._feature = optimizable_tensors["feature"]
            self._direction = optimizable_tensors["direction"]
            self._sharpness = optimizable_tensors["sharpness"]
            
            # Reset other attributes
            self.xyz_gradient_accum = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
            self.denom = torch.zeros((self.get_xyz.shape[0], 1), device="cuda")
            self.max_radii2D = torch.zeros((self.get_xyz.shape[0]), device="cuda")

        # Prune original points
        prune_filter = torch.cat((selected_pts_mask, torch.zeros(N * selected_pts_mask.sum(), device="cuda", dtype=bool)))
        self.prune_points(prune_filter)
